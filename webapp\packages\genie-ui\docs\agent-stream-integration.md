# Agent Stream 流式数据集成方案

## 概述

本文档描述了如何在 Genie UI 中集成 `agent_stream` 工具的流式数据展示功能。当 `messageType === "tool_result"` 且 `toolName === "agent_stream"` 时，系统会在对话区域直接展示流式数据，同时在右侧工作空间提供专门的流式输出界面。

## 数据流程

### 1. 后端数据结构

```typescript
interface ToolResult {
  toolName: "agent_stream";
  toolResult: string; // 流式数据内容
}

interface EventData {
  messageType: "tool_result";
  resultMap: {
    toolResult: ToolResult;
    isFinal: boolean; // 是否为最终数据
  };
}
```

### 2. 前端处理流程

1. **数据接收**: SSE 流式接收 `agent_stream` 数据
2. **数据处理**: `handleToolResultMessage` 函数特殊处理流式数据累积
3. **界面展示**: 
   - 对话区域：直接展示流式内容
   - 工作空间：使用专门的 `AgentStreamRenderer` 组件

## 核心组件

### AgentStreamRenderer

专门用于展示 `agent_stream` 流式数据的组件。

**特性:**
- 自动滚动到底部
- 流式状态指示
- 光标动画效果
- 完成状态显示

**使用方式:**
```tsx
<AgentStreamRenderer 
  content={streamContent}
  isStreaming={!isFinal}
  className="h-full"
/>
```

### 对话区域展示

在 `Dialogue` 组件中，`agent_stream` 会直接在对话流中展示：

```tsx
if (tool.messageType === "tool_result" && tool.toolResult?.toolName === "agent_stream") {
  // 直接展示流式内容，不需要点击进入工作空间
  return <StreamingDisplay />;
}
```

## 配置说明

### 1. 流式数据累积

在 `chat.ts` 中的 `handleToolResultMessage` 函数会：
- 累积流式数据到 `resultMap.streamData`
- 更新 `toolResult.toolResult` 为完整内容
- 根据 `isFinal` 标记流式状态

### 2. 内容渲染

在 `useContent.ts` 中：
```typescript
if (toolResult?.toolName === 'agent_stream') {
  markDownContent = resultMap?.streamData || toolResult?.toolResult || '';
}
```

### 3. 动作信息

在 `buildAction` 函数中为 `agent_stream` 提供专门的动作描述：
```typescript
case "agent_stream":
  return {
    action: "智能体流式输出",
    tool: "智能体", 
    name: "正在生成内容..."
  };
```

## 使用场景

1. **实时内容生成**: 大模型生成长文本时的实时展示
2. **代码生成**: 逐步生成代码的过程展示
3. **分析报告**: 实时生成分析结果的过程
4. **对话回复**: 智能体回复的流式展示

## 注意事项

1. **性能优化**: 大量流式数据时注意 DOM 更新性能
2. **错误处理**: 流式中断时的错误状态处理
3. **用户体验**: 提供清晰的流式状态指示
4. **内存管理**: 长时间流式数据的内存占用控制

## 扩展性

该方案可以轻松扩展支持其他流式工具：
1. 在 `handleToolResultMessage` 中添加新的工具名称判断
2. 在 `useContent.ts` 中添加对应的内容处理逻辑
3. 根据需要创建专门的渲染组件
