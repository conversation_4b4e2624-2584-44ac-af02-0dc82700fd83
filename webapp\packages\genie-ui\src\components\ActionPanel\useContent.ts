import { PanelItemType } from "./type";

const useContent =  (taskItem?: PanelItemType) => {
  let markDownContent = '';

  // let fileUrl = '';
  if (!taskItem) {
    return {
      markDownContent,
      // fileUrl
    };
  }

  const { messageType, toolResult, resultMap } = taskItem;

  // const [fileInfo] = resultMap.fileInfo ?? [];

  switch (messageType) {
    case 'tool_result':
      // 特殊处理 bank_data_report 的流式数据
      if (toolResult?.toolName === 'bank_data_report') {
        // 对于 bank_data_report，优先使用 resultMap.data 或 resultMap.codeOutput 来支持流式更新
        markDownContent = resultMap?.data || resultMap?.codeOutput || toolResult?.toolResult || '';
      } else if (toolResult?.toolName === 'agent_stream') {
        // 特殊处理 agent_stream 的流式数据
        markDownContent = resultMap?.streamData || toolResult?.toolResult || '';
      } else {
        markDownContent = toolResult?.toolResult || '';
      }
      break;
    case 'code':
      if (resultMap?.code || (resultMap?.codeOutput && resultMap?.isFinal)) {
        const text = resultMap?.code || resultMap?.codeOutput;
        markDownContent = `\`\`\`python\n${text}\n\`\`\``;
      }
      break;
    case 'markdown':
    case 'html':
      markDownContent = resultMap?.codeOutput || '';
      break;
    case 'deep_search':
    case 'report':
      markDownContent = resultMap.answer || '';
      break;
    // case 'file':
    //   fileUrl = fileInfo.domainUrl;
    //   break;
  }
  return {
    markDownContent,
    // fileUrl
  };
};

export default useContent;