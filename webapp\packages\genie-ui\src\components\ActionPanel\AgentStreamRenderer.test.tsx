import React from 'react';
import { render, screen } from '@testing-library/react';
import AgentStreamRenderer from './AgentStreamRenderer';

describe('AgentStreamRenderer', () => {
  it('renders streaming content correctly', () => {
    const content = 'This is streaming content...';
    
    render(
      <AgentStreamRenderer 
        content={content}
        isStreaming={true}
      />
    );
    
    expect(screen.getByText('智能体流式输出')).toBeInTheDocument();
    expect(screen.getByText('正在输出...')).toBeInTheDocument();
    expect(screen.getByText(content)).toBeInTheDocument();
  });

  it('renders completed content correctly', () => {
    const content = 'This is completed content.';
    
    render(
      <AgentStreamRenderer 
        content={content}
        isStreaming={false}
      />
    );
    
    expect(screen.getByText('智能体流式输出')).toBeInTheDocument();
    expect(screen.getByText('输出完成')).toBeInTheDocument();
    expect(screen.getByText(content)).toBeInTheDocument();
  });

  it('renders empty state correctly', () => {
    render(
      <AgentStreamRenderer 
        content=""
        isStreaming={false}
      />
    );
    
    expect(screen.getByText('暂无输出内容')).toBeInTheDocument();
  });

  it('renders waiting state correctly', () => {
    render(
      <AgentStreamRenderer 
        content=""
        isStreaming={true}
      />
    );
    
    expect(screen.getByText('等待智能体输出...')).toBeInTheDocument();
  });
});
