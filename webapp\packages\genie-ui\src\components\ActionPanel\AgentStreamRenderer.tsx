import React, { useEffect, useRef } from 'react';
import classNames from 'classnames';
import { LoadingSpinner } from '../LoadingSpinner';

interface AgentStreamRendererProps {
  content: string;
  isStreaming?: boolean;
  className?: string;
}

/**
 * Agent Stream 流式数据渲染器
 * 专门用于展示 agent_stream 工具的流式输出数据
 */
const AgentStreamRenderer: React.FC<AgentStreamRendererProps> = ({
  content,
  isStreaming = false,
  className
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (containerRef.current && isStreaming) {
      const container = containerRef.current;
      container.scrollTop = container.scrollHeight;
    }
  }, [content, isStreaming]);

  return (
    <div 
      ref={containerRef}
      className={classNames(
        'w-full h-full overflow-auto bg-white rounded-[8px] border border-[#e9e9f0]',
        className
      )}
    >
      <div className="sticky top-0 bg-white border-b border-[#e9e9f0] px-16 py-8 flex items-center justify-between">
        <div className="flex items-center">
          <span className="text-[14px] font-medium text-[#202945]">智能体流式输出</span>
          {isStreaming && (
            <div className="flex items-center ml-8">
              <LoadingSpinner color="#4040FF" size="small" />
              <span className="ml-4 text-[12px] text-[#4040FF]">正在输出...</span>
            </div>
          )}
        </div>
        {!isStreaming && content && (
          <span className="text-[12px] text-[#2029459E]">输出完成</span>
        )}
      </div>
      
      <div className="p-16">
        <div 
          ref={contentRef}
          className="whitespace-pre-wrap break-words text-[14px] leading-[22px] text-[#202945] min-h-[100px]"
        >
          {content || (
            <div className="text-[#2029459E] italic">
              {isStreaming ? '等待智能体输出...' : '暂无输出内容'}
            </div>
          )}
          {isStreaming && content && (
            <span className="inline-block w-2 h-5 bg-[#4040FF] animate-pulse ml-1 align-text-bottom">
              |
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentStreamRenderer;
